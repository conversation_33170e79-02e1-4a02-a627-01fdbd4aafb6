/* General styles */
:root {
  --bg-color: #f4f4f9;
  --text-color: #333;
  --card-bg: #fff;
  --card-border: #ddd;
  --button-bg: #007BFF;
  --button-text: #fff;
  --button-hover-bg: #0056b3;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --reset-bg: #ff664ba3;
  --reset-hover: #fe2600bd;
  --stat-color: #4CAF50;
  --option-color: #00000060;
  --option-color-dark: #ffffff60;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
}

.dark-mode {
  --bg-color: #121212;
  --text-color: #e0e0e0;
  --card-bg: #1e1e1e;
  --card-border: #444;
  --button-bg: #1a73e8;
  --button-hover-bg: #135aba;
  --shadow-color: rgba(0, 0, 0, 0.6);
  --reset-bg: #ff300c72;
  --reset-hover: #ff26008e;
  --stat-color: #69d26d;
}

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
}

p {
  margin-top: 0;
}

h1, h2, h3, p, button {
  transition: color 0.3s ease, background-color 0.3s ease;
}

h1 {
  text-align: center;
  margin: 15px;
  font-size: 2.5rem;
}

h2, h3 {
  margin-bottom: 10px;
}

.option-label {
  color: var(--option-color);
}

.dark-mode .option-label {
  color: var(--option-color-dark);
}

.stat {
  display: inline-block;
  padding: 10px;
  margin: 5px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 5px;
  cursor: pointer;
}

.stat:hover {
  background-color: #e0e0e0;
}

.stat-title, .stat-item {
  color: var(--stat-color);
  font-weight: bold;
}

.stat-title {
  font-size: 1.2em;
  margin-bottom: 10px;
}

.stat-item {
  font-size: 1em;
}

/* Layout */
main {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;
  padding: 20px;
}

#statSelection, #selectedCombo {
  flex: 1 1 calc(65% - 40px);
  background-color: var(--card-bg);
  border-radius: 10px;
  box-shadow: 0 4px 8px var(--shadow-color);
  padding: 20px;
  border: 1px solid var(--card-border);
}

#selectedCombo {
  flex: 1 1 calc(30% - 40px);
}

/* Search Section */
.search-section {
  background-color: var(--card-bg);
  border-radius: 10px;
  box-shadow: 0 4px 8px var(--shadow-color);
  padding: 20px;
  margin: 20px;
  border: 1px solid var(--card-border);
}

.search-container {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

#statSearch {
  flex: 1;
  padding: 10px;
  border: 1px solid var(--card-border);
  border-radius: 5px;
  background-color: var(--card-bg);
  color: var(--text-color);
  font-size: 1rem;
}

#clearSearch {
  padding: 10px 15px;
  background-color: var(--danger-color);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 16px;
  border: 1px solid var(--card-border);
  background-color: var(--card-bg);
  color: var(--text-color);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover, .filter-btn.active {
  background-color: var(--button-bg);
  color: var(--button-text);
}

/* Buttons */
.stat-button, .replace-option {
  padding: 8px 12px;
  border: none;
  margin: 3px;
  border-radius: 20px;
  background-color: var(--button-bg);
  color: var(--button-text);
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.stat-button:hover, .replace-option:hover {
  background-color: var(--button-hover-bg);
}

.stat-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(55, 186, 125, 0.5);
  background-color: #4CAF50;
}

.stat-button.selected, .dark-mode .stat-button.selected {
  background-color: #388E3C !important;
  color: white !important;
}

.stat-button.hidden {
  display: none;
}

.replace-option small {
  display: block;
  font-size: 0.8em;
  color: var(--card-border);
}

.dark-mode .replace-option small {
  color: #aaa;
}

.replace-option {
  text-align: left;
  background-color: var(--button-bg);
  color: var(--button-text);
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-content {
  background-color: var(--card-bg);
  margin: 15% auto;
  padding: 20px;
  max-width: 220px;
  border-radius: 5px;
  text-align: center;
}

.export-modal, .import-modal, .share-modal {
  max-width: 500px;
  margin: 10% auto;
}

.export-modal textarea, .import-modal textarea, .share-modal input {
  width: 100%;
  height: 150px;
  margin: 10px 0;
  padding: 10px;
  border: 1px solid var(--card-border);
  border-radius: 5px;
  background-color: var(--card-bg);
  color: var(--text-color);
  font-family: monospace;
  resize: vertical;
}

.share-modal input {
  height: auto;
}

.close-modal {
  font-size: 30px;
  cursor: pointer;
  color: var(--reset-bg);
  float: right;
  margin-top: -10px;
}

.close-modal:hover {
  color: var(--reset-hover);
}

.dark-mode .modal-content {
  background-color: #333;
  color: white;
}

.modal-content .replace-option {
  display: block;
  width: 100%;
  margin: 10px auto;
}

/* Main buttons container */
.main-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  width: 100%;
  margin: 20px 0;
  flex-wrap: wrap;
}

/* Enhanced buttons */
#exportButton, #importButton, #shareButton {
  background-color: var(--info-color);
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px var(--shadow-color);
  transition: background-color 0.3s ease;
}

#exportButton:hover, #importButton:hover, #shareButton:hover {
  background-color: #138496;
}

/* Toggle button */
#darkModeToggle {
  top: 20px;
  right: 20px;
  padding: 10px 15px;
  background-color: var(--card-bg);
  color: var(--text-color);
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  box-shadow: 0 2px 4px var(--shadow-color);
  transition: background-color 0.3s ease;
}

#darkModeToggle:hover {
  background-color: var(--shadow-color);
}

/* Reset button */
#resetButton {
  background-color: var(--reset-bg);
  color: var(--text-color);
  border: none;
  border-radius: 5px;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px var(--shadow-color);
  transition: background-color 0.3s ease;
}

#resetButton:hover {
  background-color: var(--reset-hover);
}

/* Dropdown styles */
#tierSelect {
  padding: 10px;
  font-size: 1rem;
  border: 1px solid var(--card-border);
  border-radius: 5px;
  width: 50%;
  max-width: 200px;
  background-color: var(--card-bg);
  color: var(--text-color);
  box-shadow: 0 2px 4px var(--shadow-color);
}

/* Stats Summary */
#statsSummary {
  background-color: var(--card-bg);
  border: 2px solid var(--stat-color);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
}

#summaryContent {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.summary-category {
  background-color: var(--bg-color);
  padding: 10px;
  border-radius: 5px;
  border: 1px solid var(--card-border);
}

.summary-category h4 {
  margin: 0 0 10px 0;
  color: var(--stat-color);
  font-size: 1.1em;
}

.summary-stat {
  display: flex;
  justify-content: space-between;
  margin: 5px 0;
  font-size: 0.9em;
}

#toggleSummary {
  background-color: var(--stat-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

#toggleSummary:hover {
  background-color: #45a049;
}

/* Responsive design */
@media (max-width: 768px) {
  .main-buttons {
    flex-direction: column;
    gap: 5px;
  }
  
  #tierSelect {
    width: 80%;
  }
  
  main {
    flex-direction: column;
  }
  
  #statSelection, #selectedCombo {
    flex: 1 1 100%;
  }
  
  .filter-buttons {
    justify-content: center;
  }
}
