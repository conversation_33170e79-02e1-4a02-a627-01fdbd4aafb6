<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Suivline Simulator Enhanced</title>
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>

  <h1>Suivline Caractéristique Enhanced</h1>

  <!-- Main buttons container -->
  <div class="main-buttons">
    <!-- Dark Mode Toggle -->
    <button id="darkModeToggle"><i id="darkModeIcon" class="fas fa-moon"></i></button>

    <!-- Reset Button -->
    <button id="resetButton">
      <i class="fas fa-sync-alt"></i> Reset
    </button>

    <!-- Tier Select dropdown -->
    <select id="tierSelect">
      <option value="-1">Sélectionnez un Tier</option>
      <option value="0">Tier 1 -- (1 stat)</option>
      <option value="1">Tier 2 -- (2 stats)</option>
      <option value="2">Tier 3 -- (3 stats)</option>
      <option value="3">Tier 4 -- (4 stats)</option>
      <option value="4">Tier 5 -- (5 stats)</option>
      <option value="5">S Tier -- (2 stats)</option>
      <option value="6">SS Tier -- (3 stats)</option>
    </select>

    <!-- New Enhancement Buttons -->
    <button id="exportButton" title="Export Build">
      <i class="fas fa-download"></i> Export
    </button>
    
    <button id="importButton" title="Import Build">
      <i class="fas fa-upload"></i> Import
    </button>
    
    <button id="shareButton" title="Share Build">
      <i class="fas fa-share-alt"></i> Share
    </button>
  </div>

  <!-- Search/Filter Section -->
  <div class="search-section" style="display:none;">
    <div class="search-container">
      <input type="text" id="statSearch" placeholder="Rechercher une statistique..." />
      <button id="clearSearch"><i class="fas fa-times"></i></button>
    </div>
    <div class="filter-buttons">
      <button class="filter-btn active" data-filter="all">Tous</button>
      <button class="filter-btn" data-filter="damage">Dommages</button>
      <button class="filter-btn" data-filter="resistance">Résistance</button>
      <button class="filter-btn" data-filter="stats">Statistiques</button>
      <button class="filter-btn" data-filter="utility">Utilité</button>
    </div>
  </div>

  <!-- Label Selection Modal -->
  <div id="labelModal" class="modal">
    <div class="modal-content">
      <p>Assign to which label?</p>
      <button class="label-option" data-label="0">Option 1</button>
      <button class="label-option" data-label="1">Option 2</button>
    </div>
  </div>

  <!-- Replacement Modal -->
  <div id="replaceModal" class="modal">
    <div class="modal-content">
      <p>Replace which existing stat?</p>
    </div>
  </div>

  <!-- Export Modal -->
  <div id="exportModal" class="modal">
    <div class="modal-content export-modal">
      <span class="close-modal">&times;</span>
      <h3>Export Build</h3>
      <textarea id="exportText" readonly placeholder="Your build code will appear here..."></textarea>
      <button id="copyExportButton">Copy to Clipboard</button>
    </div>
  </div>

  <!-- Import Modal -->
  <div id="importModal" class="modal">
    <div class="modal-content import-modal">
      <span class="close-modal">&times;</span>
      <h3>Import Build</h3>
      <textarea id="importText" placeholder="Paste your build code here..."></textarea>
      <button id="importBuildButton">Import Build</button>
    </div>
  </div>

  <!-- Share Modal -->
  <div id="shareModal" class="modal">
    <div class="modal-content share-modal">
      <span class="close-modal">&times;</span>
      <h3>Share Build</h3>
      <p>Share this URL:</p>
      <input type="text" id="shareUrl" readonly />
      <button id="copyShareButton">Copy URL</button>
    </div>
  </div>

  <main>
    <!-- Stats Selection Container -->
    <div id="statSelection" style="display:none;">
      <h2>Choisissez les statistiques:</h2>
      <div id="statsContainer"></div>
    </div>

    <!-- Output Selected Stats -->
    <div id="selectedCombo">
      <!-- Stats Summary will be added here -->
      <div id="statsSummary" style="display:none;">
        <h3>Résumé des Statistiques</h3>
        <div id="summaryContent"></div>
        <button id="toggleSummary">Masquer le Résumé</button>
      </div>
    </div>
  </main>

  <script src="data.js"></script>
  <script src="script.js"></script>
</body>
</html>
