// Tiers and stats data
const tiers = [
  {
    tierName: "Tier 1",
    stats: [
      { 
        name: "Option 1", 
        values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "600 Initiative"],
        categories: ["stats", "stats", "stats", "stats", "damage", "stats", "utility"]
      }
    ]
  },
  {
    tierName: "Tier 2",
    stats: [
      { 
        name: "Option 1", 
        values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "600 Initiative"],
        categories: ["stats", "stats", "stats", "stats", "damage", "stats", "utility"]
      },
      { 
        name: "Option 2", 
        values: ["20 Esquive PA", "20 Esquive PM", "20 Retrait PA", "20 Retrait PM", "40 Sagesse", "20 Tacle", "20 Fuite", "60 Prospection"],
        categories: ["utility", "utility", "utility", "utility", "stats", "utility", "utility", "utility"]
      }
    ]
  },
  {
    tierName: "Tier 3",
    stats: [
      { 
        name: "Option 1", 
        values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "600 Initiative"],
        categories: ["stats", "stats", "stats", "stats", "damage", "stats", "utility"]
      },
      { 
        name: "Option 2", 
        values: ["20 Esquive PA", "20 Esquive PM", "20 Retrait PA", "20 Retrait PM", "40 Sagesse", "20 Tacle", "20 Fuite", "60 Prospection"],
        categories: ["utility", "utility", "utility", "utility", "stats", "utility", "utility", "utility"]
      },
      { 
        name: "Option 3", 
        values: ["7% Résistance Neutre", "7% Résistance Terre", "7% Résistance Feu", "7% Résistance Eau", "7% Résistance Air", 
                 "30 Résistance Neutre", "30 Résistance Terre", "30 Résistance Feu", "30 Résistance Eau", "30 Résistance Air", 
                 "25 Résistance Critique", "40 Résistance Poussée"],
        categories: ["resistance", "resistance", "resistance", "resistance", "resistance", 
                     "resistance", "resistance", "resistance", "resistance", "resistance", 
                     "resistance", "resistance"]
      }
    ]
  },
  {
    tierName: "Tier 4",
    stats: [
      { 
        name: "Option 1", 
        values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "600 Initiative"],
        categories: ["stats", "stats", "stats", "stats", "damage", "stats", "utility"]
      },
      { 
        name: "Option 2", 
        values: ["20 Esquive PA", "20 Esquive PM", "20 Retrait PA", "20 Retrait PM", "40 Sagesse", "20 Tacle", "20 Fuite", "60 Prospection"],
        categories: ["utility", "utility", "utility", "utility", "stats", "utility", "utility", "utility"]
      },
      { 
        name: "Option 3", 
        values: ["7% Résistance Neutre", "7% Résistance Terre", "7% Résistance Feu", "7% Résistance Eau", "7% Résistance Air", 
                 "30 Résistance Neutre", "30 Résistance Terre", "30 Résistance Feu", "30 Résistance Eau", "30 Résistance Air", 
                 "25 Résistance Critique", "40 Résistance Poussée"],
        categories: ["resistance", "resistance", "resistance", "resistance", "resistance", 
                     "resistance", "resistance", "resistance", "resistance", "resistance", 
                     "resistance", "resistance"]
      },
      { 
        name: "Option 4", 
        values: ["10 Dommages Neutre", "10 Dommages Terre", "10 Dommages Feu", "10 Dommages Eau", "10 Dommages Air",
                 "25 Dommages Critique", "40 Dommages Possée", "7% Critique", "25 Soins"],
        categories: ["damage", "damage", "damage", "damage", "damage", "damage", "damage", "damage", "utility"]
      }
    ]
  },
  {
    tierName: "Tier 5",
    stats: [
      { 
        name: "Option 1", 
        values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "600 Initiative"],
        categories: ["stats", "stats", "stats", "stats", "damage", "stats", "utility"]
      },
      { 
        name: "Option 2", 
        values: ["20 Esquive PA", "20 Esquive PM", "20 Retrait PA", "20 Retrait PM", "40 Sagesse", "20 Tacle", "20 Fuite", "60 Prospection"],
        categories: ["utility", "utility", "utility", "utility", "stats", "utility", "utility", "utility"]
      },
      { 
        name: "Option 3", 
        values: ["7% Résistance Neutre", "7% Résistance Terre", "7% Résistance Feu", "7% Résistance Eau", "7% Résistance Air", 
                 "30 Résistance Neutre", "30 Résistance Terre", "30 Résistance Feu", "30 Résistance Eau", "30 Résistance Air", 
                 "25 Résistance Critique", "40 Résistance Poussée"],
        categories: ["resistance", "resistance", "resistance", "resistance", "resistance", 
                     "resistance", "resistance", "resistance", "resistance", "resistance", 
                     "resistance", "resistance"]
      },
      { 
        name: "Option 4", 
        values: ["10 Dommages Neutre", "10 Dommages Terre", "10 Dommages Feu", "10 Dommages Eau", "10 Dommages Air",
                 "25 Dommages Critique", "40 Dommages Possée", "7% Critique", "25 Soins"],
        categories: ["damage", "damage", "damage", "damage", "damage", "damage", "damage", "damage", "utility"]
      },
      { 
        name: "Option 5", 
        values: ["1 PO", "2% Résistance aux Sorts", "4% Résistance d'Armes", "3% Résistance Distance", "3% Résistance Mêlée",
                 "1 Invocation", "2% Dommages aux Sorts", "4% Dommages d'Armes", "3% Dommages Distance", "3% Dommages Mêlée"],
        categories: ["utility", "resistance", "resistance", "resistance", "resistance", 
                     "utility", "damage", "damage", "damage", "damage"]
      }
    ]
  },
  {
    tierName: "S Tier",
    stats: [
      { 
        name: "Option 1", 
        values: ["130 Force", "130 Intelligence", "130 Chance", "130 Agilité", "100 Puissance"],
        categories: ["stats", "stats", "stats", "stats", "damage"]
      },
      { 
        name: "Option 2", 
        values: ["400 Vita", "1500 Initiative"],
        categories: ["stats", "utility"]
      },
      { 
        name: "Option 3", 
        values: ["40 Esquive PA", "40 Esquive PM", "40 Retrait PA", "40 Retrait PM", "80 Sagesse"],
        categories: ["utility", "utility", "utility", "utility", "stats"]
      },
      { 
        name: "Option 4", 
        values: ["40 Tacle", "40 Fuite", "100 Prospection"],
        categories: ["utility", "utility", "utility"]
      },
      { 
        name: "Option 5", 
        values: ["15% Résistance Neutre", "15% Résistance Terre", "15% Résistance Feu", "15% Résistance Eau", "15% Résistance Air",
                 "70 Résistance Neutre", "70 Résistance Terre", "70 Résistance Feu", "70 Résistance Eau", "70 Résistance Air"],
        categories: ["resistance", "resistance", "resistance", "resistance", "resistance",
                     "resistance", "resistance", "resistance", "resistance", "resistance"]
      },
      { 
        name: "Option 6", 
        values: ["60 Résistance Critique", "80 Résistance Poussée"],
        categories: ["resistance", "resistance"]
      },
      { 
        name: "Option 7", 
        values: ["25 Dommages Neutre", "25 Dommages Terre", "25 Dommages Feu", "25 Dommages Eau", "25 Dommages Air",
                 "40 Dommages Critique", "80 Dommages Possée"],
        categories: ["damage", "damage", "damage", "damage", "damage", "damage", "damage"]
      },
      { 
        name: "Option 8", 
        values: ["15% Critique", "50 Soin"],
        categories: ["damage", "utility"]
      },
      { 
        name: "Option 9", 
        values: ["2 PO", "6% Résistance aux Sorts", "8% Résistance d'Armes", "7% Résistance Distance", "7% Résistance Mêlée"],
        categories: ["utility", "resistance", "resistance", "resistance", "resistance"]
      },
      { 
        name: "Option 10", 
        values: ["3 Invocation", "6% Dommages aux Sorts", "8% Dommages d'Armes", "7% Dommages Distance", "7% Dommages Mêlée"],
        categories: ["utility", "damage", "damage", "damage", "damage"]
      },
      { 
        name: "Option 11", 
        values: ["1 PA", "1 PM"],
        categories: ["utility", "utility"]
      }
    ]
  },
  {
    tierName: "SS Tier",
    stats: [
      { 
        name: "Option 1", 
        values: ["100 Force", "100 Intelligence", "100 Chance", "100 Agilité", "80 Puissance"],
        categories: ["stats", "stats", "stats", "stats", "damage"]
      },
      { 
        name: "Option 2", 
        values: ["300 Vita", "1000 Initiative"],
        categories: ["stats", "utility"]
      },
      { 
        name: "Option 3", 
        values: ["30 Esquive PA", "30 Esquive PM", "30 Retrait PA", "30 Retrait PM", "60 Sagesse"],
        categories: ["utility", "utility", "utility", "utility", "stats"]
      },
      { 
        name: "Option 4", 
        values: ["30 Tacle", "30 Fuite", "80 Prospection"],
        categories: ["utility", "utility", "utility"]
      },
      { 
        name: "Option 5", 
        values: ["10% Résistance Neutre", "10% Résistance Terre", "10% Résistance Feu", "10% Résistance Eau", "10% Résistance Air",
                 "50 Résistance Neutre", "50 Résistance Terre", "50 Résistance Feu", "50 Résistance Eau", "50 Résistance Air"],
        categories: ["resistance", "resistance", "resistance", "resistance", "resistance",
                     "resistance", "resistance", "resistance", "resistance", "resistance"]
      },
      { 
        name: "Option 6", 
        values: ["40 Résistance Critique", "60 Résistance Poussée"],
        categories: ["resistance", "resistance"]
      },
      { 
        name: "Option 7", 
        values: ["20 Dommages Neutre", "20 Dommages Terre", "20 Dommages Feu", "20 Dommages Eau", "20 Dommages Air",
                 "30 Dommages Critique", "60 Dommages Possée"],
        categories: ["damage", "damage", "damage", "damage", "damage", "damage", "damage"]
      },
      { 
        name: "Option 8", 
        values: ["10% Critique", "35 Soin"],
        categories: ["damage", "utility"]
      },
      { 
        name: "Option 9", 
        values: ["1 PO", "4% Résistance aux Sorts", "6% Résistance d'Armes", "5% Résistance Distance", "5% Résistance Mêlée"],
        categories: ["utility", "resistance", "resistance", "resistance", "resistance"]
      },
      { 
        name: "Option 10", 
        values: ["2 Invocation", "4% Dommages aux Sorts", "6% Dommages d'Armes", "5% Dommages Distance", "5% Dommages Mêlée"],
        categories: ["utility", "damage", "damage", "damage", "damage"]
      },
      { 
        name: "Option 11", 
        values: ["1 PA", "1 PM"],
        categories: ["utility", "utility"]
      }
    ]
  }
];

// Maximum number of stats allowed for S Tier and SS Tier
const tier6Labels = ["Option 1", "Option 2"]; 
const tier7Labels = ["Option 1", "Option 2", "Option 3"]; 

const MAX_STATS_S_TIER = 2;
const MAX_STATS_SS_TIER = 3;
