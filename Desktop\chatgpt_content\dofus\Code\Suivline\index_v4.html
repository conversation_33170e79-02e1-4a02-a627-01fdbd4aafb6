<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Suivline Simulator</title>
  <style>
    /* General styles */
:root {
  --bg-color: #f4f4f9;
  --text-color: #333;
  --card-bg: #fff;
  --card-border: #ddd;
  --button-bg: #007BFF;
  --button-text: #fff;
  --button-hover-bg: #0056b3;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --reset-bg: #ff664ba3;
  --reset-hover: #fe2600bd;
  --stat-color: #4CAF50;
  --option-color: #00000060;
  --option-color-dark: #ffffff60;

}

.dark-mode {
  --bg-color: #121212;
  --text-color: #e0e0e0;
  --card-bg: #1e1e1e;
  --card-border: #444;
  --button-bg: #1a73e8;
  --button-hover-bg: #135aba;
  --shadow-color: rgba(0, 0, 0, 0.6);
  --reset-bg: #ff300c72;
  --reset-hover: #ff26008e;
  --stat-color: #69d26d;
}

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
}
p{
  margin-top: 0;
  /* margin-bottom: 10px; */
}
h1, h2, h3, p, button {
  transition: color 0.3s ease, background-color 0.3s ease;
}

h1 {
  text-align: center;
  margin: 15px;
  font-size: 2.5rem;
}

h2, h3 {
  margin-bottom: 10px;
}

.option-label {
  color: var(--option-color);
}

.dark-mode .option-label {
  color: var(--option-color-dark);
}

.stat {
  display: inline-block;
  padding: 10px;
  margin: 5px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 5px;
  cursor: pointer;
}

.stat:hover {
  background-color: #e0e0e0;
}

.stat-title, .stat-item {
  color: var(--stat-color);
  font-weight: bold;
}

.stat-title {
  font-size: 1.2em;
  margin-bottom: 10px;
}

.stat-item {
  font-size: 1em;
}

/* Layout */
main {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;
  padding: 20px;
}

#statSelection, #selectedCombo {
  flex: 1 1 calc(65% - 40px);
  background-color: var(--card-bg);
  border-radius: 10px;
  box-shadow: 0 4px 8px var(--shadow-color);
  padding: 20px;
  border: 1px solid var(--card-border);
}

#selectedCombo {
  flex: 1 1 calc(30% - 40px);
}

/* Buttons */
.stat-button, .replace-option {
  padding: 8px 12px;
  border: none;
  margin: 3px;
  border-radius: 20px;
  background-color: var(--button-bg);
  color: var(--button-text);
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.stat-button:hover, .replace-option:hover {
  background-color: var(--button-hover-bg);

}

.stat-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(55, 186, 125, 0.5);
  background-color: #4CAF50;
}

.stat-button.selected, .dark-mode .stat-button.selected {
  background-color: #388E3C !important;
  color: white !important;
}

.replace-option small {
  display: block;
  font-size: 0.8em;
  color: var(--card-border);
}

.dark-mode .replace-option small {
  color: #aaa;
}
.replace-option {
  /* margin: 10px;
  padding: 15px; */
  text-align: left;
  background-color: var(--button-bg);
  color: var(--button-text);
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-content {
  background-color: var(--card-bg);
  margin: 15% auto;
  padding: 20px;
  max-width: 220px;
  border-radius: 5px;
  text-align: center;
}
.close-modal {
  font-size: 30px;
  cursor: pointer;
  color: var(--reset-bg);
}

.close-modal:hover {
  color: var(--reset-hover);
}
.dark-mode .modal-content {
  background-color: #333;
  color: white;
}

.modal-content .replace-option {
  display: block;
  width: 100%;
  margin: 10px auto;
}

/* Toggle button */
#darkModeToggle {
  top: 20px;
  right: 20px;
  padding: 10px 15px;
  background-color: var(--card-bg);
  color: var(--text-color);
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  box-shadow: 0 2px 4px var(--shadow-color);
  transition: background-color 0.3s ease;
}

#darkModeToggle:hover {
  background-color: var(--shadow-color);
}

/* Main buttons container */
.main-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  width: 100%;
  margin: 20px 0;
}

/* Reset button */
#resetButton {
  background-color: var(--reset-bg);
  color: var(--text-color);
  border: none;
  border-radius: 5px;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px var(--shadow-color);
  transition: background-color 0.3s ease;
}

#resetButton:hover {
  background-color: var(--reset-hover);
}

/* Dropdown styles */
#tierSelect {
  padding: 10px;
  font-size: 1rem;
  border: 1px solid var(--card-border);
  border-radius: 5px;
  width: 50%;
  max-width: 200px;
  background-color: var(--card-bg);
  color: var(--text-color);
  box-shadow: 0 2px 4px var(--shadow-color);
}

  </style>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>

  <h1>Suivline Caractéristique</h1>

  <!-- Main buttons container -->
  <div class="main-buttons">
    <!-- Dark Mode Toggle -->
    <button id="darkModeToggle"><i id="darkModeIcon" class="fas fa-moon"></i></button>

    <!-- Reset Button -->
    <button id="resetButton">
      <i class="fas fa-sync-alt"></i> Reset
    </button>

    <!-- Tier Select dropdown -->
    <select id="tierSelect">
      <option value="-1">Sélectionnez un Tier</option>
      <option value="0">Tier 1 -- (1 stat)</option>
      <option value="1">Tier 2 -- (2 stats)</option>
      <option value="2">Tier 3 -- (3 stats)</option>
      <option value="3">Tier 4 -- (4 stats)</option>
      <option value="4">Tier 5 -- (5 stats)</option>
      <option value="5">S Tier -- (2 stats)</option>
      <option value="6">SS Tier -- (3 stats)</option>
    </select>
  </div>

  <!-- Label Selection Modal -->
  <div id="labelModal" class="modal">
    <div class="modal-content">
      <p>Assign to which label?</p>
      <button class="label-option" data-label="0">Option 1</button>
      <button class="label-option" data-label="1">Option 2</button>
    </div>
  </div>

  <!-- Replacement Modal -->
  <div id="replaceModal" class="modal">
    <div class="modal-content">
      <p>Replace which existing stat?</p>
    </div>
  </div>

  <main>
    <!-- Stats Selection Container -->
    <div id="statSelection" style="display:none;">
      <h2>Choisissez les statistiques:</h2>
      <div id="statsContainer"></div>
    </div>

    <!-- Output Selected Stats -->
    <div id="selectedCombo"></div>
  </main>

  <script>

    // Load the selected stats from localStorage on page load
document.addEventListener("DOMContentLoaded", () => {
  // Check if there are saved selected stats in localStorage
  const savedStats = localStorage.getItem("selectedStats");
  if (savedStats) {
    selectedStats = JSON.parse(savedStats); // Parse the saved data
    displaySelectedStats(); // Display the loaded stats
  }

  // Check and apply the saved dark mode preference on page load
  const darkModePreference = localStorage.getItem("darkMode");
  if (darkModePreference === "enabled") {
    document.body.classList.add("dark-mode");
    const toggleButton = document.getElementById("darkModeToggle");
    if (toggleButton) toggleButton.checked = true; // Sync toggle state if it's a checkbox or similar
  }
});

// Save the selected stats to localStorage
function saveSelectedStats() {
  localStorage.setItem("selectedStats", JSON.stringify(selectedStats)); // Save as JSON string
}

// Tiers and stats data
const tiers = [
  {
    tierName: "Tier 1",
    stats: [
      { name: "Option 1", values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "600 Initiative"] },
  
    ]
    
  },
  {
    tierName: "Tier 2",
    stats: [
      { name: "Option 1", values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "600 Initiative"] },
      { name: "Option 2", values: ["20 Esquive PA", "20 Esquive PM", "20 Retrait PA", "20 Retrait PM", "40 Sagesse",
                                   "20 Tacle", "20 Fuite", "60 Prospection"] },
      
    ]
    
  },
  {
    tierName: "Tier 3",
    stats: [
      { name: "Option 1", values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "600 Initiative"] },
      { name: "Option 2", values: ["20 Esquive PA", "20 Esquive PM", "20 Retrait PA", "20 Retrait PM", "40 Sagesse",
                                   "20 Tacle", "20 Fuite", "60 Prospection"] },
      { name: "Option 3", values: ["7% Résistance Neutre", "7% Résistance Terre", "7% Résistance Feu", "7% Résistance Eau", "7% Résistance Air" ,
                                   "30 Résistance Neutre", "30 Résistance Terre", "30 Résistance Feu", "30 Résistance Eau", "30 Résistance Air" ,
                                   "25 Résistance Critique" , "40 Résistance Poussée"] },
      
    ]
    
  },
  {
    tierName: "Tier 4",
    stats: [
      { name: "Option 1", values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "600 Initiative"] },
      { name: "Option 2", values: ["20 Esquive PA", "20 Esquive PM", "20 Retrait PA", "20 Retrait PM", "40 Sagesse",
                                   "20 Tacle", "20 Fuite", "60 Prospection"] },
      { name: "Option 3", values: ["7% Résistance Neutre", "7% Résistance Terre", "7% Résistance Feu", "7% Résistance Eau", "7% Résistance Air" ,
                                   "30 Résistance Neutre", "30 Résistance Terre", "30 Résistance Feu", "30 Résistance Eau", "30 Résistance Air" ,
                                   "25 Résistance Critique" , "40 Résistance Poussée"] },
      { name: "Option 4", values: ["10 Dommages Neutre", "10 Dommages Terre","10 Dommages Feu","10 Dommages Eau","10 Dommages Air",
                                   "25 Dommages Critique", "40 Dommages Possée",
                                   "7% Critique","25 Soins"] },
     
    ]
    
  },
  {
    tierName: "Tier 5",
    stats: [
      { name: "Option 1", values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "600 Initiative"] },
      { name: "Option 2", values: ["20 Esquive PA", "20 Esquive PM", "20 Retrait PA", "20 Retrait PM", "40 Sagesse",
                                   "20 Tacle", "20 Fuite", "60 Prospection"] },
      { name: "Option 3", values: ["7% Résistance Neutre", "7% Résistance Terre", "7% Résistance Feu", "7% Résistance Eau", "7% Résistance Air" ,
                                   "30 Résistance Neutre", "30 Résistance Terre", "30 Résistance Feu", "30 Résistance Eau", "30 Résistance Air" ,
                                   "25 Résistance Critique" , "40 Résistance Poussée"] },
      { name: "Option 4", values: ["10 Dommages Neutre", "10 Dommages Terre","10 Dommages Feu","10 Dommages Eau","10 Dommages Air",
                                   "25 Dommages Critique", "40 Dommages Possée",
                                   "7% Critique","25 Soins"] },
      { name: "Option 5", values: ["1 PO", "2% Résistance aux Sorts", "4% Résistance d'Armes" ,"3% Résistance Distance","3% Résistance Mêlée",
                                   "1 Invocation", "2% Dommages aux Sorts", "4% Dommages d'Armes" ,"3% Dommages Distance","3% Dommages Mêlée" ] }
    ]
    
  },
  {
    tierName: "S Tier",
    stats: [
      { name: "Option 1", values: ["130 Force", "130 Intelligence", "130 Chance", "130 Agilité", "100 Puissance"] },
      { name: "Option 2", values: ["400 Vita", "1500 Initiative"] },
      { name: "Option 3", values: ["40 Esquive PA","40 Esquive PM", "40 Retrait PA","40 Retrait PM", "80 Sagesse"] },
      { name: "Option 4", values: ["40 Tacle", "40 Fuite", "100 Prospection"] },
      { name: "Option 5", values: ["15% Résistance Neutre", "15% Résistance Terre", "15% Résistance Feu", "15% Résistance Eau", "15% Résistance Air",
                                   "70 Résistance Neutre", "70 Résistance Terre", "70 Résistance Feu", "70 Résistance Eau", "70 Résistance Air"] },
      { name: "Option 6", values: ["60 Résistance Critique", "80 Résistance Poussée"] },
      { name: "Option 7", values: ["25 Dommages Neutre", "25 Dommages Terre","25 Dommages Feu","25 Dommages Eau","25 Dommages Air",
                                   "40 Dommages Critique", "80 Dommages Possée"] },
      { name: "Option 8", values: ["15% Critique", "50 Soin"] },
      { name: "Option 9", values: ["2 PO", "6% Résistance aux Sorts", "8% Résistance d'Armes" ,"7% Résistance Distance","7% Résistance Mêlée"] },
      { name: "Option 10", values:["3 Invocation", "6% Dommages aux Sorts", "8% Dommages d'Armes" ,"7% Dommages Distance","7% Dommages Mêlée"] },
      { name: "Option 11", values:["1 PA", "1 PM"] }
    ]
  },
  {
    tierName : "SS Tier",
    stats: [
      { name: "Option 1", values: ["100 Force", "100 Intelligence", "100 Chance", "100 Agilité", "80 Puissance"] },
      { name: "Option 2", values: ["300 Vita", "1000 Initiative"] },
      { name: "Option 3", values: ["30 Esquive PA","30 Esquive PM", "30 Retrait PA","30 Retrait PM", "60 Sagesse"] },
      { name: "Option 4", values: ["30 Tacle", "30 Fuite", "80 Prospection"] },
      { name: "Option 5", values: ["10% Résistance Neutre", "10% Résistance Terre", "10% Résistance Feu", "10% Résistance Eau", "10% Résistance Air",
                                   "50 Résistance Neutre", "50 Résistance Terre", "50 Résistance Feu", "50 Résistance Eau", "50 Résistance Air"] },
      { name: "Option 6", values: ["40 Résistance Critique", "60 Résistance Poussée"] },
      { name: "Option 7", values: ["20 Dommages Neutre", "20 Dommages Terre","20 Dommages Feu","20 Dommages Eau","20 Dommages Air",
                                   "30 Dommages Critique", "60 Dommages Possée"] },
      { name: "Option 8", values: ["10% Critique", "35 Soin"] },
      { name: "Option 9", values: ["1 PO","4% Résistance aux Sorts", "6% Résistance d'Armes" ,"5% Résistance Distance","5% Résistance Mêlée"] },
      { name: "Option 10", values: ["2 Invocation", "4% Dommages aux Sorts", "6% Dommages d'Armes" ,"5% Dommages Distance","5% Dommages Mêlée",] },
      { name: "Option 11", values: ["1 PA", "1 PM"] }
    ]
  }
  
];

// Maximum number of stats allowed for S Tier
const tier6Labels = ["Option 1", "Option 2"]; 
// Maximum number of stats allowed for SS Tier
const tier7Labels = ["Option 1", "Option 2", "Option 3"]; 

const MAX_STATS_S_TIER = 2;
const MAX_STATS_SS_TIER = 3;

function selectStat(tier, groupIndex, statValue) {
  while (selectedStats.length <= tier) {
    selectedStats.push([]);
  }

  if (tier === 5 || tier === 6) { // S Tier or SS Tier
    const tierStats = selectedStats[tier];
    const existingIndex = tierStats.findIndex(s => s?.groupIndex === groupIndex);

    if (existingIndex > -1) {
      // If replacing same group, just update the value
      tierStats[existingIndex].stat = statValue;
    } else if (tierStats.length < (tier === 5 ? MAX_STATS_S_TIER : MAX_STATS_SS_TIER)) {
      // Add new selection if space available
      tierStats.push({ groupIndex, stat: statValue });
    } else {
      // Show replacement modal if full
      showReplacementModal(groupIndex, statValue, tier);
      return;
    }
  } else {
    // Normal tier selection
    selectedStats[tier][groupIndex] = statValue;
  }

  saveSelectedStats();
  displaySelectedStats();
  loadTierStats(); // Force reload to update button states
}
function showReplacementModal(newGroupIndex, newStatValue, tier) {
  const modal = document.getElementById('replaceModal');
  const content = modal.querySelector('.modal-content');

  // Clear previous content
  content.innerHTML = `
    <span class="close-modal">&times;</span>
    <p>Choisissez pour remplacer</p>
  `;

  // Close button functionality
  content.querySelector('.close-modal').onclick = (e) => {
    e.preventDefault(); // Prevent immediate state change
    modal.style.display = 'none';
    
    // Use a small timeout to ensure the modal is fully closed
    setTimeout(() => {
      loadTierStats(); // Force reload after modal is completely closed
    }, 50); // 50ms delay to ensure visual transition completes
  };

  // Buttons for existing stats
  selectedStats[tier].forEach((existingStat, index) => {
    const btn = document.createElement('button');
    btn.className = 'replace-option';
    btn.innerHTML = `${existingStat.stat}<br><small>${tiers[tier].stats[existingStat.groupIndex].name}</small>`;
    btn.onclick = (e) => {
      e.preventDefault(); // Prevent immediate state change
      
      // Update the selection
      selectedStats[tier][index] = { 
        groupIndex: newGroupIndex, 
        stat: newStatValue 
      };
      
      saveSelectedStats();
      displaySelectedStats();
      
      // Close modal and update states after visual transition
      modal.style.display = 'none';
      setTimeout(() => {
        loadTierStats(); // Force reload after modal is completely closed
      }, 50); // 50ms delay to ensure visual transition completes
    };
    content.appendChild(btn);
  });

  modal.style.display = 'block';
}
function showLabelModal(statValue, tier) {
  const modal = document.getElementById('labelModal');
  modal.style.display = 'block';

  const labelButtons = document.querySelectorAll('.label-option');
  labelButtons.forEach(button => {
    button.onclick = () => {
      const labelIndex = parseInt(button.dataset.label);
      selectedStats[tier][labelIndex] = statValue;
      saveSelectedStats();
      displaySelectedStats();
      modal.style.display = 'none';
    };
  });
}

// Close modal when clicking outside
window.onclick = function(event) {
  const modal = document.getElementById('labelModal');
  if (event.target === modal) {
    modal.style.display = 'none';
  }
};


// Load stats based on selected tier
function loadTierStats() {
  const tierSelect = document.getElementById("tierSelect");
  const statsContainer = document.getElementById("statsContainer");
  const selectedTier = parseInt(tierSelect.value);

  // Reset previous selections and stats
  statsContainer.innerHTML = "";
  if (selectedTier >= 0) {
    const tierData = tiers[selectedTier].stats;

    // Display stats for the selected tier as a single option per line
    tierData.forEach((statGroup, groupIndex) => {
      const statDiv = document.createElement("div");
      statDiv.classList.add("tier-container");

      // Create a title for the group (e.g., "Option 1")
      const statTitle = document.createElement("h3");
      statTitle.textContent = statGroup.name;
      statDiv.appendChild(statTitle);

      // Create buttons for each stat value in the group
      const statGroupDiv = document.createElement("div");
      statGroupDiv.classList.add("stat-group");

      statGroup.values.forEach(value => {
        const statButton = document.createElement("button");
        statButton.textContent = value;
        statButton.classList.add("stat-button");
        
        // Check if this stat is selected
        const isSelected = selectedStats[selectedTier]?.some(entry => {
          if (selectedTier === 5 || selectedTier === 6) { // S Tier
            return entry.stat === value && entry.groupIndex === groupIndex;
          }
          return selectedStats[selectedTier]?.[groupIndex] === value;
        });
        
        if (isSelected) statButton.classList.add('selected');
        
        statButton.onclick = () => selectStat(selectedTier, groupIndex, value);
        
        statGroupDiv.appendChild(statButton);
      });

      statDiv.appendChild(statGroupDiv);
      statsContainer.appendChild(statDiv);
    });
  }

  // Show or hide the selection container
  document.getElementById("statSelection").style.display = selectedTier >= 0 ? "block" : "none";
}


// Display the selected stats
function displaySelectedStats() {
  const selectedCombo = document.getElementById("selectedCombo");
  selectedCombo.innerHTML = "<h2>Suivlines:</h2>";

  selectedStats.forEach((tierStats, tierIndex) => {
    if (tierStats.length > 0) {
      const tierDiv = document.createElement("div");
      tierDiv.classList.add("selected-tier");

      const tierTitle = document.createElement("h3");
      tierTitle.textContent = tiers[tierIndex].tierName;
      tierDiv.appendChild(tierTitle);

      if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
        tierStats.forEach((statEntry) => {
          const statWrapper = document.createElement("div");
          statWrapper.classList.add("stat-item");
          
          // Add the option label with class for SS Tier
          const groupNameSpan = document.createElement("span");
          groupNameSpan.className = "option-label";
          groupNameSpan.textContent = `${tiers[tierIndex].stats[statEntry.groupIndex].name}: `;
          
          statWrapper.appendChild(groupNameSpan);
          statWrapper.appendChild(document.createTextNode(statEntry.stat));
          tierDiv.appendChild(statWrapper);
        });
      } else {
        tierStats.forEach((stat, groupIndex) => {
          if (stat) {
            const statWrapper = document.createElement("div");
            statWrapper.classList.add("stat-item");
            // Original code for non-SS tiers
            statWrapper.innerHTML = `<span class="option-label">Option ${groupIndex + 1}:</span> ${stat}`;
            tierDiv.appendChild(statWrapper);
          }
        });
      }

      selectedCombo.appendChild(tierDiv);
    }
  });
}
// Initialize default values for `selectedStats`
let selectedStats = [];
// Event listener for tier dropdown change
document.getElementById("tierSelect").addEventListener("change", loadTierStats);
// Dark mode toggle functionality
document.getElementById("darkModeToggle").addEventListener("click", () => {
  const isDarkMode = document.body.classList.toggle("dark-mode");

  // Save the preference to localStorage
  if (isDarkMode) {
    localStorage.setItem("darkMode", "enabled");
  } else {
    localStorage.setItem("darkMode", "disabled");
  }
});


// Reset button functionality
document.getElementById("resetButton").addEventListener("click", () => {
  // Clear the selected stats array
  selectedStats = [];

  // Remove the saved stats from localStorage
  localStorage.removeItem("selectedStats");

  // Clear the displayed selected stats
  const selectedCombo = document.getElementById("selectedCombo");
  selectedCombo.innerHTML = ""; // This clears the current selected stats display
  
  // Show the stat selection container again
  document.getElementById("statSelection").style.display = "none";

  // Reset the tier selection dropdown to the first option (Tier 1)
  const tierSelect = document.getElementById("tierSelect");
  tierSelect.value = "-1"; // Assuming the first option in the dropdown is "Tier 1"
  
  // Call loadTierStats to reload stats for Tier 1
  loadTierStats();
});


  </script>
</body>
</html>